package com.zerosense.bluetooth

import android.bluetooth.*
import android.bluetooth.le.*
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * BLE（蓝牙低功耗）管理器
 * 负责BLE设备的连接、特征值订阅和数据接收
 */
class BleManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "BleManager"
        
        @Volatile
        private var INSTANCE: BleManager? = null
        
        fun getInstance(context: Context): BleManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BleManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // 点云灵动键服务和特征值UUID
        private val SERVICE_FFE0_UUID = UUID.fromString("0000FFE0-0000-1000-8000-00805F9B34FB") // 主服务
        private val CHARACTERISTIC_FFE1_UUID = UUID.fromString("0000FFE1-0000-1000-8000-00805F9B34FB") // 设备合法性验证
        private val CHARACTERISTIC_FFE4_UUID = UUID.fromString("0000FFE4-0000-1000-8000-00805F9B34FB") // 按键通知
        private val CHARACTERISTIC_FFE5_UUID = UUID.fromString("0000FFE5-0000-1000-8000-00805F9B34FB") // 设备基本信息
        
        // 客户端特征配置描述符UUID（用于启用通知）
        private val CLIENT_CHARACTERISTIC_CONFIG_UUID = UUID.fromString("00002902-0000-1000-8000-00805F9B34FB")
        
        // BLE扫描超时时间
        private const val BLE_SCAN_TIMEOUT = 10000L
    }
    
    private val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private val bluetoothLeScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    private val handler = Handler(Looper.getMainLooper())
    
    // GATT连接管理
    private val gattConnections = ConcurrentHashMap<String, BluetoothGatt>()
    private val connectionCallbacks = ConcurrentHashMap<String, BluetoothGattCallback>()
    
    // 回调管理
    private val callbacks = mutableSetOf<BluetoothCallback>()
    
    // 扫描相关
    private var isScanning = false
    private var scanCallback: ScanCallback? = null
    
    /**
     * 添加回调监听器
     */
    fun addCallback(callback: BluetoothCallback) {
        callbacks.add(callback)
    }
    
    /**
     * 移除回调监听器
     */
    fun removeCallback(callback: BluetoothCallback) {
        callbacks.remove(callback)
    }
    
    /**
     * 开始BLE扫描
     */
    fun startBleScan(): Boolean {
        if (bluetoothLeScanner == null) {
            notifyError("BLE扫描器不可用")
            return false
        }
        
        if (isScanning) {
            notifyInfo("BLE扫描已在进行中")
            return true
        }
        
        scanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                val device = result.device
                val rssi = result.rssi
                
                Log.d(TAG, "发现BLE设备: ${device.name ?: "未知"} (${device.address}) RSSI: $rssi")
                
                val deviceInfo = BluetoothDeviceInfo.fromBluetoothDevice(device, rssi)
                notifyDeviceFound(deviceInfo)
            }
            
            override fun onScanFailed(errorCode: Int) {
                notifyError("BLE扫描失败，错误代码: $errorCode")
                isScanning = false
            }
        }
        
        try {
            bluetoothLeScanner.startScan(scanCallback)
            isScanning = true
            notifyInfo("开始BLE扫描")
            
            // 设置扫描超时
            handler.postDelayed({
                stopBleScan()
            }, BLE_SCAN_TIMEOUT)
            
            return true
        } catch (e: Exception) {
            notifyError("启动BLE扫描失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 停止BLE扫描
     */
    fun stopBleScan() {
        if (!isScanning) return
        
        try {
            scanCallback?.let { bluetoothLeScanner?.stopScan(it) }
            isScanning = false
            notifyInfo("BLE扫描已停止")
        } catch (e: Exception) {
            notifyError("停止BLE扫描失败: ${e.message}")
        }
    }
    
    /**
     * 连接到BLE设备
     */
    fun connectToBleDevice(deviceAddress: String): Boolean {
        Log.d(TAG, "开始连接BLE设备: $deviceAddress")

        if (bluetoothAdapter == null) {
            notifyError("蓝牙适配器不可用")
            return false
        }

        if (!bluetoothAdapter.isEnabled) {
            notifyError("蓝牙未启用，请先启用蓝牙")
            return false
        }

        // 检查设备地址格式
        if (!BluetoothAdapter.checkBluetoothAddress(deviceAddress)) {
            notifyError("无效的设备地址: $deviceAddress")
            return false
        }

        // 停止BLE扫描以提高连接成功率
        if (isScanning) {
            Log.d(TAG, "停止BLE扫描以提高连接成功率")
            stopBleScan()
        }

        val device = bluetoothAdapter.getRemoteDevice(deviceAddress)
        if (device == null) {
            notifyError("设备不存在: $deviceAddress")
            return false
        }

        // 如果已经连接，直接返回成功
        if (gattConnections.containsKey(deviceAddress)) {
            notifyInfo("设备已连接: $deviceAddress")
            return true
        }
        
        val gattCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                Log.d(TAG, "BLE连接状态变化: $deviceAddress, status: $status, newState: $newState")

                // 检查连接状态
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    // 连接失败
                    val errorMsg = when (status) {
                        133 -> "连接失败: GATT错误133 (设备不可达或连接被拒绝)"
                        8 -> "连接失败: 连接超时"
                        19 -> "连接失败: 设备未找到或不可用"
                        22 -> "连接失败: 连接被远程设备终止"
                        else -> "连接失败: GATT错误 $status"
                    }
                    Log.e(TAG, errorMsg)
                    handler.post {
                        notifyError(errorMsg)
                        notifyBleConnectionStateChanged(deviceAddress, BluetoothProfile.STATE_DISCONNECTED)
                    }
                    // 清理连接
                    gattConnections.remove(deviceAddress)
                    connectionCallbacks.remove(deviceAddress)
                    gatt.close()
                    return
                }

                when (newState) {
                    BluetoothProfile.STATE_CONNECTED -> {
                        Log.d(TAG, "BLE设备已连接: $deviceAddress")
                        handler.post {
                            notifySuccess("BLE设备连接成功: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }
                        // 发现服务
                        Log.d(TAG, "开始发现服务...")
                        val discoverResult = gatt.discoverServices()
                        if (!discoverResult) {
                            Log.e(TAG, "启动服务发现失败")
                            handler.post {
                                notifyError("启动服务发现失败")
                            }
                        }
                    }
                    BluetoothProfile.STATE_DISCONNECTED -> {
                        Log.d(TAG, "BLE设备已断开: $deviceAddress")
                        handler.post {
                            notifyInfo("BLE设备已断开: $deviceAddress")
                            notifyBleConnectionStateChanged(deviceAddress, newState)
                        }
                        // 清理连接
                        gattConnections.remove(deviceAddress)
                        connectionCallbacks.remove(deviceAddress)
                        gatt.close()
                    }
                    BluetoothProfile.STATE_CONNECTING -> {
                        Log.d(TAG, "BLE设备正在连接: $deviceAddress")
                        handler.post {
                            notifyInfo("正在连接BLE设备: $deviceAddress")
                        }
                    }
                    BluetoothProfile.STATE_DISCONNECTING -> {
                        Log.d(TAG, "BLE设备正在断开: $deviceAddress")
                        handler.post {
                            notifyInfo("正在断开BLE设备: $deviceAddress")
                        }
                    }
                }
            }
            
            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                Log.d(TAG, "服务发现回调: $deviceAddress, status: $status")
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "服务发现成功: $deviceAddress")
                    Log.d(TAG, "发现的服务数量: ${gatt.services.size}")

                    // 打印所有发现的服务
                    for (service in gatt.services) {
                        Log.d(TAG, "发现服务: ${service.uuid}")
                    }

                    handler.post {
                        notifyInfo("服务发现成功，发现${gatt.services.size}个服务")
                        // 点云灵动键初始化流程 - 基于客户代码
                        initializeDotixDevice(deviceAddress)
                    }
                } else {
                    val errorMsg = when (status) {
                        129 -> "服务发现失败: GATT内部错误"
                        133 -> "服务发现失败: GATT连接错误"
                        else -> "服务发现失败: GATT错误 $status"
                    }
                    Log.e(TAG, "$errorMsg: $deviceAddress")
                    handler.post {
                        notifyError(errorMsg)
                    }
                }
            }
            
            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic) {
                val uuid = characteristic.uuid.toString().uppercase()
                val data = characteristic.value

                Log.d(TAG, "🔔 收到特征值通知!")
                Log.d(TAG, "  设备地址: $deviceAddress")
                Log.d(TAG, "  特征值UUID: $uuid")
                Log.d(TAG, "  数据长度: ${data?.size ?: 0}")
                if (data != null && data.isNotEmpty()) {
                    val dataHex = data.joinToString(" ") { "%02X".format(it) }
                    Log.d(TAG, "  数据内容: $dataHex")
                }

                handler.post {
                    data?.let {
                        Log.d(TAG, "转发特征值通知到回调")
                        notifyCharacteristicNotification(deviceAddress, uuid, it)
                    }
                }
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt, descriptor: BluetoothGattDescriptor, status: Int) {
                val characteristicUuid = descriptor.characteristic.uuid.toString().uppercase()
                Log.d(TAG, "📝 描述符写入完成")
                Log.d(TAG, "  设备地址: $deviceAddress")
                Log.d(TAG, "  特征值UUID: $characteristicUuid")
                Log.d(TAG, "  描述符UUID: ${descriptor.uuid}")
                Log.d(TAG, "  写入状态: $status")

                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "✅ 特征值通知订阅成功!")
                    handler.post {
                        notifySuccess("特征值通知订阅成功: $characteristicUuid")
                    }
                } else {
                    Log.e(TAG, "❌ 特征值通知订阅失败，状态码: $status")
                    handler.post {
                        notifyError("特征值通知订阅失败，状态码: $status")
                    }
                }
            }
        }
        
        try {
            val gatt = device.connectGatt(context, false, gattCallback)
            gattConnections[deviceAddress] = gatt
            connectionCallbacks[deviceAddress] = gattCallback

            // 设置连接超时
            handler.postDelayed({
                if (gattConnections.containsKey(deviceAddress)) {
                    val currentGatt = gattConnections[deviceAddress]
                    if (currentGatt != null) {
                        Log.w(TAG, "BLE连接超时: $deviceAddress")
                        notifyError("BLE连接超时: $deviceAddress")
                        currentGatt.disconnect()
                        currentGatt.close()
                        gattConnections.remove(deviceAddress)
                        connectionCallbacks.remove(deviceAddress)
                    }
                }
            }, BLE_SCAN_TIMEOUT)

            notifyInfo("正在连接BLE设备: $deviceAddress")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "连接BLE设备异常: ${e.message}", e)
            notifyError("连接BLE设备失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 订阅特征值通知
     */
    fun subscribeToCharacteristic(deviceAddress: String, characteristicUuid: UUID): Boolean {
        Log.d(TAG, "开始订阅特征值: $deviceAddress, UUID: $characteristicUuid")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            val error = "设备未连接: $deviceAddress"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }

        Log.d(TAG, "GATT连接存在，开始查找特征值")

        // 打印所有可用的服务和特征值
        Log.d(TAG, "可用的服务和特征值:")
        for (service in gatt.services) {
            Log.d(TAG, "服务: ${service.uuid}")
            for (characteristic in service.characteristics) {
                Log.d(TAG, "  特征值: ${characteristic.uuid}, 属性: ${characteristic.properties}")
            }
        }

        // 查找特征值
        var targetCharacteristic: BluetoothGattCharacteristic? = null
        for (service in gatt.services) {
            for (characteristic in service.characteristics) {
                if (characteristic.uuid == characteristicUuid) {
                    targetCharacteristic = characteristic
                    Log.d(TAG, "找到目标特征值: ${characteristic.uuid}")
                    break
                }
            }
            if (targetCharacteristic != null) break
        }

        if (targetCharacteristic == null) {
            val error = "未找到特征值: ${characteristicUuid}"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }
        
        // 检查特征值是否支持通知
        val properties = targetCharacteristic.properties
        Log.d(TAG, "特征值属性: $properties")
        if ((properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) == 0) {
            val error = "特征值不支持通知功能"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }

        // 启用本地通知
        Log.d(TAG, "启用本地通知...")
        val success = gatt.setCharacteristicNotification(targetCharacteristic, true)
        if (!success) {
            val error = "启用本地通知失败"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }
        Log.d(TAG, "本地通知启用成功")

        // 写入客户端特征配置描述符以启用远程通知
        Log.d(TAG, "查找客户端特征配置描述符...")
        val descriptor = targetCharacteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
        if (descriptor != null) {
            Log.d(TAG, "找到描述符，写入通知配置...")
            descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
            val writeSuccess = gatt.writeDescriptor(descriptor)
            if (writeSuccess) {
                val success = "成功订阅特征值通知: ${characteristicUuid}"
                Log.d(TAG, success)
                notifySuccess(success)
                return true
            } else {
                val error = "写入描述符失败"
                Log.e(TAG, error)
                notifyError(error)
                return false
            }
        } else {
            val error = "未找到客户端特征配置描述符 (UUID: $CLIENT_CHARACTERISTIC_CONFIG_UUID)"
            Log.e(TAG, error)
            notifyError(error)
            return false
        }
    }
    
    /**
     * 断开BLE设备连接
     */
    fun disconnectBleDevice(deviceAddress: String) {
        val gatt = gattConnections[deviceAddress]
        gatt?.disconnect()
    }
    
    /**
     * 获取已连接的BLE设备列表
     */
    fun getConnectedBleDevices(): List<String> {
        return gattConnections.keys.toList()
    }

    /**
     * 诊断BLE设备的详细信息
     */
    fun diagnoseBleDevice(deviceAddress: String): Boolean {
        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备未连接，无法诊断: $deviceAddress")
            notifyError("设备未连接，无法诊断: $deviceAddress")
            return false
        }

        Log.d(TAG, "🔍 开始诊断点云灵动键设备: $deviceAddress")
        Log.d(TAG, "====================================================")

        var serviceCount = 0
        var characteristicCount = 0
        var ffe0ServiceFound = false // 主服务
        var ffe1Found = false // 验证特征值
        var ffe4Found = false // 按键通知特征值
        var ffe5Found = false // 设备信息特征值

        for (service in gatt.services) {
            serviceCount++
            val serviceUuid = service.uuid.toString().uppercase()
            Log.d(TAG, "📋 服务 #$serviceCount: $serviceUuid")
            Log.d(TAG, "   类型: ${if (service.type == BluetoothGattService.SERVICE_TYPE_PRIMARY) "主服务" else "次服务"}")

            // 检查是否是0xFFE0主服务
            if (serviceUuid.contains("FFE0")) {
                ffe0ServiceFound = true
                Log.d(TAG, "   🎯 找到0xFFE0主服务!")
            }

            for (characteristic in service.characteristics) {
                characteristicCount++
                val uuid = characteristic.uuid.toString().uppercase()
                val properties = characteristic.properties

                Log.d(TAG, "   📄 特征值 #$characteristicCount: $uuid")
                Log.d(TAG, "      属性值: $properties")

                // 解析属性
                val propertyList = mutableListOf<String>()
                if (properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) propertyList.add("READ")
                if (properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) propertyList.add("WRITE")
                if (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) propertyList.add("WRITE_NO_RESPONSE")
                if (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) propertyList.add("NOTIFY")
                if (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) propertyList.add("INDICATE")

                Log.d(TAG, "      支持操作: ${propertyList.joinToString(", ")}")

                // 检查是否是0xFFE4特征值
                if (uuid.contains("FFE4")) {
                    ffe4Found = true
                    Log.d(TAG, "      🎯 找到0xFFE4特征值!")

                    // 检查描述符
                    val descriptors = characteristic.descriptors
                    Log.d(TAG, "      描述符数量: ${descriptors.size}")
                    for (descriptor in descriptors) {
                        Log.d(TAG, "        📝 描述符: ${descriptor.uuid}")
                        if (descriptor.uuid.toString().uppercase().contains("2902")) {
                            Log.d(TAG, "        🎯 找到客户端特征配置描述符!")
                        }
                    }
                }
            }
        }

        Log.d(TAG, "====================================================")
        Log.d(TAG, "📊 点云灵动键诊断总结:")
        Log.d(TAG, "   服务总数: $serviceCount")
        Log.d(TAG, "   特征值总数: $characteristicCount")
        Log.d(TAG, "   0xFFE0 (主服务): ${if (ffe0ServiceFound) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE1 (验证): ${if (ffe1Found) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE4 (按键): ${if (ffe4Found) "✅ 找到" else "❌ 未找到"}")
        Log.d(TAG, "   0xFFE5 (信息): ${if (ffe5Found) "✅ 找到" else "❌ 未找到"}")

        // 关键：必须有0xFFE0服务和0xFFE4特征值才能正常通讯
        val isCompatible = ffe0ServiceFound && ffe4Found
        Log.d(TAG, "   设备兼容性: ${if (isCompatible) "✅ 兼容" else "❌ 不兼容"}")

        if (!ffe0ServiceFound) {
            Log.e(TAG, "   ❌ 缺少0xFFE0主服务，无法建立通讯")
        }
        if (!ffe4Found) {
            Log.e(TAG, "   ❌ 缺少0xFFE4按键通知特征值")
        }

        Log.d(TAG, "====================================================")

        handler.post {
            if (isCompatible) {
                val features = mutableListOf<String>()
                if (ffe0ServiceFound) features.add("主服务")
                if (ffe1Found) features.add("设备验证")
                if (ffe4Found) features.add("按键通知")
                if (ffe5Found) features.add("设备信息")

                notifySuccess("点云灵动键诊断完成: 设备完全兼容，支持功能: ${features.joinToString(", ")}")
            } else {
                val missing = mutableListOf<String>()
                if (!ffe0ServiceFound) missing.add("0xFFE0主服务")
                if (!ffe4Found) missing.add("0xFFE4按键通知")

                notifyError("诊断完成: 设备不兼容，缺少关键组件: ${missing.joinToString(", ")}")
            }
        }

        return isCompatible
    }
    
    // 通知方法
    private fun notifyDeviceFound(device: BluetoothDeviceInfo) {
        callbacks.forEach { it.onDeviceFound(device) }
    }
    
    private fun notifyError(error: String) {
        callbacks.forEach { it.onError(error) }
    }
    
    private fun notifySuccess(message: String) {
        callbacks.forEach { it.onSuccess(message) }
    }
    
    private fun notifyInfo(message: String) {
        callbacks.forEach { it.onInfo(message) }
    }
    
    private fun notifyCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
        callbacks.forEach { it.onCharacteristicNotification(deviceAddress, characteristicUuid, data) }
    }
    
    private fun notifyBleConnectionStateChanged(deviceAddress: String, state: Int) {
        callbacks.forEach { it.onBleConnectionStateChanged(deviceAddress, state) }
    }
    
    /**
     * 初始化点云灵动键设备
     * 关键：确保连接到0xFFE0服务才能正常通讯
     */
    private fun initializeDotixDevice(deviceAddress: String) {
        Log.d(TAG, "🚀 开始初始化点云灵动键: $deviceAddress")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备未连接，无法初始化: $deviceAddress")
            return
        }

        // 关键步骤：确保连接到0xFFE0服务
        val ffe0Service = gatt.getService(SERVICE_FFE0_UUID)
        if (ffe0Service == null) {
            Log.e(TAG, "❌ 未找到0xFFE0服务，设备可能不兼容")
            handler.post {
                notifyError("未找到0xFFE0服务，无法建立通讯")
            }
            return
        }

        Log.d(TAG, "✅ 找到0xFFE0服务，开始初始化通讯")

        // 步骤1: 立即订阅按键通知 (0xFFE4) - 这是关键！
        handler.postDelayed({
            Log.d(TAG, "📱 订阅0xFFE4按键通知特征值")
            subscribeToFFE4Characteristic(deviceAddress, ffe0Service)
        }, 500)

        // 步骤2: 读取设备基本信息 (0xFFE5)
        handler.postDelayed({
            readDeviceInfo(deviceAddress, ffe0Service)
        }, 1000)
    }

    /**
     * 专门订阅0xFFE4特征值的方法
     * 确保在正确的服务下进行订阅
     * 基于客户提供的工作代码实现，包含关键的100ms等待
     */
    private fun subscribeToFFE4Characteristic(deviceAddress: String, ffe0Service: BluetoothGattService) {
        val ffe4Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE4_UUID)
        if (ffe4Characteristic == null) {
            Log.e(TAG, "❌ 在0xFFE0服务中未找到0xFFE4特征值")
            handler.post {
                notifyError("未找到0xFFE4按键通知特征值")
            }
            return
        }

        Log.d(TAG, "✅ 在0xFFE0服务中找到0xFFE4特征值")

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备连接已断开: $deviceAddress")
            return
        }

        // 步骤1: 启用通知
        val success = gatt.setCharacteristicNotification(ffe4Characteristic, true)
        if (!success) {
            Log.e(TAG, "❌ 启用0xFFE4特征值通知失败")
            handler.post {
                notifyError("启用按键通知失败")
            }
            return
        }

        Log.d(TAG, "✅ 0xFFE4特征值通知已启用")

        // 步骤2: 关键等待100ms（参考客户提供的工作代码）
        handler.postDelayed({
            Log.d(TAG, "⏰ 修改Characteristic Notification后，等待100ms完成")

            // 步骤3: 写入客户端特征配置描述符
            val descriptor = ffe4Characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
            if (descriptor != null) {
                Log.d(TAG, "📝 写入客户端特征配置描述符")
                descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                val writeSuccess = gatt.writeDescriptor(descriptor)
                if (writeSuccess) {
                    Log.d(TAG, "✅ 描述符写入请求已发送")
                    handler.post {
                        notifySuccess("点云灵动键按键通知订阅成功，可以开始按键测试")
                    }
                } else {
                    Log.e(TAG, "❌ 描述符写入请求失败")
                    handler.post {
                        notifyError("订阅按键通知失败")
                    }
                }
            } else {
                Log.e(TAG, "❌ 未找到客户端特征配置描述符")
                handler.post {
                    notifyError("设备不支持通知订阅")
                }
            }
        }, 100) // 关键：等待100ms，参考客户代码
    }

    /**
     * 读取设备基本信息 (0xFFE5)
     */
    private fun readDeviceInfo(deviceAddress: String, ffe0Service: BluetoothGattService) {
        val ffe5Characteristic = ffe0Service.getCharacteristic(CHARACTERISTIC_FFE5_UUID)
        if (ffe5Characteristic == null) {
            Log.w(TAG, "未找到0xFFE5设备信息特征值")
            return
        }

        val gatt = gattConnections[deviceAddress]
        if (gatt == null) {
            Log.e(TAG, "设备连接已断开: $deviceAddress")
            return
        }

        Log.d(TAG, "📖 读取设备基本信息")
        val success = gatt.readCharacteristic(ffe5Characteristic)
        if (success) {
            Log.d(TAG, "设备信息读取请求已发送")
        } else {
            Log.e(TAG, "设备信息读取请求失败")
        }
    }

    /**
     * 解析按键数据
     * 根据官方协议：
     * - 短按: 0x01 + 按键号码
     * - 长按: 0x02 + 按键号码 (200ms间隔重复发送)
     * - 低电量: 0x03 0x01
     */
    private fun parseButtonData(data: ByteArray): String {
        if (data.size < 2) {
            return "数据格式错误"
        }

        val functionCode = data[0].toInt() and 0xFF
        val buttonCode = data[1].toInt() and 0xFF

        return when (functionCode) {
            0x01 -> "短按按键 #$buttonCode"
            0x02 -> "长按按键 #$buttonCode"
            0x03 -> if (buttonCode == 0x01) "低电量警告" else "未知状态码: $buttonCode"
            else -> "未知功能码: 0x${String.format("%02X", functionCode)}, 数据: 0x${String.format("%02X", buttonCode)}"
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopBleScan()
        gattConnections.values.forEach { it.close() }
        gattConnections.clear()
        connectionCallbacks.clear()
    }
}
